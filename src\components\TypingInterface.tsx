import { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Clock, Target, Zap, CheckCircle, XCircle } from 'lucide-react';
import { Job } from '@/types';

interface TypingInterfaceProps {
  job: Job;
  onSubmit: (submittedText: string, accuracy: number, timeTaken: number) => void;
  onCancel: () => void;
}

const TypingInterface = ({ job, onSubmit, onCancel }: TypingInterfaceProps) => {
  const [inputText, setInputText] = useState('');
  const [timeLeft, setTimeLeft] = useState(job.timeLimit * 60); // Convert to seconds
  const [isActive, setIsActive] = useState(false);
  const [startTime, setStartTime] = useState<number | null>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Calculate accuracy in real-time
  const calculateAccuracy = (input: string, correct: string) => {
    if (!input) return 0;
    let correctChars = 0;
    const minLength = Math.min(input.length, correct.length);
    
    for (let i = 0; i < minLength; i++) {
      if (input[i] === correct[i]) {
        correctChars++;
      }
    }
    
    return Math.round((correctChars / input.length) * 100);
  };

  const currentAccuracy = calculateAccuracy(inputText, job.correctText);
  const progress = Math.round((inputText.length / job.correctText.length) * 100);

  // Timer logic
  useEffect(() => {
    let interval: NodeJS.Timeout;
    
    if (isActive && timeLeft > 0) {
      interval = setInterval(() => {
        setTimeLeft(timeLeft => timeLeft - 1);
      }, 1000);
    } else if (timeLeft === 0) {
      handleSubmit();
    }
    
    return () => clearInterval(interval);
  }, [isActive, timeLeft]);

  const startTyping = () => {
    setIsActive(true);
    setStartTime(Date.now());
    textareaRef.current?.focus();
  };

  const handleInputChange = (value: string) => {
    if (!isActive) {
      startTyping();
    }
    setInputText(value);
  };

  const handleSubmit = () => {
    if (!startTime) return;
    
    const timeTaken = Math.round((Date.now() - startTime) / 1000);
    const finalAccuracy = calculateAccuracy(inputText, job.correctText);
    
    setIsActive(false);
    onSubmit(inputText, finalAccuracy, timeTaken);
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'text-accent';
      case 'medium': return 'text-warning';
      case 'hard': return 'text-destructive';
      default: return 'text-muted-foreground';
    }
  };

  return (
    <div className="space-y-6">
      {/* Job Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-xl">{job.title}</CardTitle>
              <div className="flex items-center gap-4 mt-2">
                <Badge variant="outline" className={getDifficultyColor(job.difficulty)}>
                  {job.difficulty.toUpperCase()}
                </Badge>
                <div className="flex items-center gap-1 text-sm text-muted-foreground">
                  <Target className="w-4 h-4" />
                  {job.points} points
                </div>
                <div className="flex items-center gap-1 text-sm text-muted-foreground">
                  <Clock className="w-4 h-4" />
                  {job.timeLimit} min limit
                </div>
              </div>
            </div>
            <div className="text-right">
              <div className="text-2xl font-mono font-bold text-foreground">
                {formatTime(timeLeft)}
              </div>
              <div className="text-xs text-muted-foreground">Time Remaining</div>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Image and Typing Area */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Image to transcribe */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Image to Transcribe</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="relative">
              <img
                src="https://via.placeholder.com/600x400/f0f0f0/666666?text=Sample+Document+Text+Image"
                alt="Document to transcribe"
                className="w-full h-auto rounded-lg border shadow-sm"
              />
              <div className="absolute inset-0 bg-card/90 rounded-lg flex items-center justify-center">
                <div className="text-center p-8 bg-card rounded-lg shadow-lg border max-w-lg">
                  <h3 className="font-semibold text-lg mb-2">Sample Text:</h3>
                  <p className="text-sm leading-relaxed text-muted-foreground font-mono">
                    {job.correctText}
                  </p>
                  <p className="text-xs text-muted-foreground mt-4">
                    (In a real scenario, this would be an image of handwritten or printed text)
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Typing Area */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg">Your Transcription</CardTitle>
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-1 text-sm">
                  <Zap className="w-4 h-4 text-warning" />
                  <span className="font-medium">{currentAccuracy}%</span>
                  <span className="text-muted-foreground">accuracy</span>
                </div>
              </div>
            </div>
            <Progress value={progress} className="mt-2" />
            <div className="text-xs text-muted-foreground mt-1">
              {inputText.length} / {job.correctText.length} characters
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <Textarea
              ref={textareaRef}
              value={inputText}
              onChange={(e) => handleInputChange(e.target.value)}
              placeholder={!isActive ? "Click here to start typing..." : "Type the text from the image..."}
              className="min-h-[300px] font-mono text-sm leading-relaxed resize-none"
              disabled={timeLeft === 0}
            />
            
            {/* Real-time feedback */}
            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-1">
                  <CheckCircle className="w-4 h-4 text-accent" />
                  <span>Correct: {Math.round((currentAccuracy / 100) * inputText.length)}</span>
                </div>
                <div className="flex items-center gap-1">
                  <XCircle className="w-4 h-4 text-destructive" />
                  <span>Errors: {inputText.length - Math.round((currentAccuracy / 100) * inputText.length)}</span>
                </div>
              </div>
              <div className="text-muted-foreground">
                WPM: {isActive && startTime ? Math.round((inputText.length / 5) / ((Date.now() - startTime) / 60000)) : 0}
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-3 pt-4">
              <Button 
                onClick={handleSubmit}
                disabled={!inputText.trim() || !isActive}
                className="flex-1"
              >
                Submit Job
              </Button>
              <Button 
                variant="outline" 
                onClick={onCancel}
                className="px-6"
              >
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default TypingInterface;