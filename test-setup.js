// Simple test to verify our authentication setup
const testAuth = async () => {
  try {
    // Test signup
    console.log('Testing signup...');
    const signupResponse = await fetch('http://localhost:5000/api/auth/signup', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        name: 'Test User',
        email: '<EMAIL>',
        password: 'TestPassword123',
        confirmPassword: 'TestPassword123'
      }),
    });
    
    const signupData = await signupResponse.json();
    console.log('Signup response:', signupData);
    
    if (signupData.success) {
      console.log('✅ Signup successful');
      
      // Test login
      console.log('\nTesting login...');
      const loginResponse = await fetch('http://localhost:5000/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'TestPassword123'
        }),
      });
      
      const loginData = await loginResponse.json();
      console.log('Login response:', loginData);
      
      if (loginData.success) {
        console.log('✅ Login successful');
        console.log('✅ Authentication system is working!');
      } else {
        console.log('❌ Login failed');
      }
    } else {
      console.log('❌ Signup failed');
    }
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.log('Make sure the server is running on port 5000');
  }
};

// Run the test
testAuth();
