import { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import Navigation from '@/components/Navigation';
import TypingInterface from '@/components/TypingInterface';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, Clock, Target, Zap } from 'lucide-react';
import { mockJobs } from '@/data/mockData';
import { useToast } from '@/hooks/use-toast';

const JobDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [isStarted, setIsStarted] = useState(false);
  
  const job = mockJobs.find(j => j.id === id);

  if (!job) {
    return (
      <div className="min-h-screen bg-background">
        <Navigation userRole="user" />
        <main className="ml-64 p-8">
          <div className="text-center py-12">
            <h1 className="text-2xl font-bold mb-4">Job Not Found</h1>
            <Button onClick={() => navigate('/jobs')}>
              Back to Jobs
            </Button>
          </div>
        </main>
      </div>
    );
  }

  const handleJobSubmit = (submittedText: string, accuracy: number, timeTaken: number) => {
    // Calculate points based on accuracy
    const pointsEarned = Math.round(job.points * (accuracy / 100));
    
    toast({
      title: "Job Submitted Successfully! 🎉",
      description: `You earned ${pointsEarned} points with ${accuracy}% accuracy!`,
    });

    // Navigate back to jobs after a short delay
    setTimeout(() => {
      navigate('/jobs');
    }, 2000);
  };

  const handleCancel = () => {
    navigate('/jobs');
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'text-accent border-accent/20 bg-accent/10';
      case 'medium': return 'text-warning border-warning/20 bg-warning/10';
      case 'hard': return 'text-destructive border-destructive/20 bg-destructive/10';
      default: return '';
    }
  };

  if (isStarted) {
    return (
      <div className="min-h-screen bg-background">
        <Navigation userRole="user" />
        <main className="ml-64 p-8">
          <div className="mb-6">
            <Button 
              variant="ghost" 
              onClick={() => setIsStarted(false)}
              className="mb-4"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Job Details
            </Button>
          </div>
          
          <TypingInterface
            job={job}
            onSubmit={handleJobSubmit}
            onCancel={handleCancel}
          />
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <Navigation userRole="user" />
      
      <main className="ml-64 p-8">
        {/* Back Button */}
        <Button 
          variant="ghost" 
          onClick={() => navigate('/jobs')}
          className="mb-6"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Jobs
        </Button>

        {/* Job Details */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Job Info */}
          <div className="lg:col-span-2 space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle className="text-2xl mb-3">{job.title}</CardTitle>
                    <div className="flex items-center gap-3">
                      <Badge 
                        variant="outline" 
                        className={getDifficultyColor(job.difficulty)}
                      >
                        {job.difficulty.toUpperCase()}
                      </Badge>
                      <div className="flex items-center gap-1 text-sm text-muted-foreground">
                        <Clock className="w-4 h-4" />
                        {job.timeLimit} minute time limit
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-3xl font-bold text-primary">{job.points}</div>
                    <div className="text-sm text-muted-foreground">points reward</div>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <h3 className="font-semibold mb-2">Job Description</h3>
                    <p className="text-muted-foreground">
                      Transcribe the text from the provided image accurately. Focus on maintaining proper spelling, 
                      punctuation, and formatting to achieve the highest accuracy score.
                    </p>
                  </div>
                  
                  <div>
                    <h3 className="font-semibold mb-2">Text Preview</h3>
                    <div className="p-4 bg-muted/50 rounded-lg border-l-4 border-primary">
                      <p className="font-mono text-sm leading-relaxed">
                        {job.correctText}
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Instructions */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Instructions</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-start gap-3">
                    <div className="w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xs font-bold">1</div>
                    <div>
                      <h4 className="font-medium">Study the Image</h4>
                      <p className="text-sm text-muted-foreground">Take time to carefully read through the text in the image before starting.</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start gap-3">
                    <div className="w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xs font-bold">2</div>
                    <div>
                      <h4 className="font-medium">Type Accurately</h4>
                      <p className="text-sm text-muted-foreground">Focus on accuracy over speed. Each character matters for your final score.</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start gap-3">
                    <div className="w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xs font-bold">3</div>
                    <div>
                      <h4 className="font-medium">Submit Before Time Runs Out</h4>
                      <p className="text-sm text-muted-foreground">Keep an eye on the timer and submit your work before the deadline.</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Quick Stats */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Job Stats</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Target className="w-4 h-4 text-primary" />
                    <span className="text-sm">Reward</span>
                  </div>
                  <span className="font-bold">{job.points} points</span>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Clock className="w-4 h-4 text-warning" />
                    <span className="text-sm">Time Limit</span>
                  </div>
                  <span className="font-bold">{job.timeLimit} minutes</span>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Zap className="w-4 h-4 text-accent" />
                    <span className="text-sm">Difficulty</span>
                  </div>
                  <Badge variant="outline" className={getDifficultyColor(job.difficulty)}>
                    {job.difficulty}
                  </Badge>
                </div>
              </CardContent>
            </Card>

            {/* Start Job */}
            <Card>
              <CardContent className="pt-6">
                <Button 
                  onClick={() => setIsStarted(true)}
                  className="w-full h-12 text-lg"
                >
                  Start Job Now
                </Button>
                <p className="text-xs text-muted-foreground text-center mt-3">
                  Timer will start automatically when you begin typing
                </p>
              </CardContent>
            </Card>

            {/* Tips */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Pro Tips</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="text-sm text-muted-foreground">
                  • Double-check punctuation and capitalization
                </div>
                <div className="text-sm text-muted-foreground">
                  • Use spell-check if available in your browser
                </div>
                <div className="text-sm text-muted-foreground">
                  • Take breaks to avoid eye strain on longer texts
                </div>
                <div className="text-sm text-muted-foreground">
                  • Focus on accuracy over speed for better rewards
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  );
};

export default JobDetail;