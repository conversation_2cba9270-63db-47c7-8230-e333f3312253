# MongoDB Installation Guide

## Option 1: MongoDB Atlas (Cloud - Recommended)

1. **Sign up for MongoDB Atlas:**
   - Go to https://www.mongodb.com/cloud/atlas/register
   - Create a free account
   - Create a new cluster (free tier)

2. **Get Connection String:**
   - Click "Connect" on your cluster
   - Choose "Connect your application"
   - Copy the connection string
   - Replace `<password>` with your database user password

3. **Update .env file:**
   ```env
   MONGODB_URI=mongodb+srv://username:<EMAIL>/type-and-earn?retryWrites=true&w=majority
   ```

## Option 2: Local MongoDB Installation (Windows)

### Method 1: Using Chocolatey (Recommended)
```powershell
# Install Chocolatey if not installed
Set-ExecutionPolicy Bypass -Scope Process -Force; [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072; iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))

# Install MongoDB
choco install mongodb

# Start MongoDB service
net start MongoDB
```

### Method 2: Manual Installation
1. Download MongoDB Community Server from: https://www.mongodb.com/try/download/community
2. Run the installer
3. Choose "Complete" installation
4. Install as a Windows Service
5. Start the MongoDB service

### Method 3: Using Docker
```bash
# Install Docker Desktop first
# Then run MongoDB in a container
docker run -d -p 27017:27017 --name mongodb mongo:latest
```

## Verify Installation

After setting up MongoDB (local or Atlas), test the connection:

```bash
# Start the server
npm run server

# You should see: "MongoDB Connected: ..."
```

## Troubleshooting

If you get connection errors:
1. Check if MongoDB service is running (for local)
2. Verify connection string (for Atlas)
3. Check firewall settings
4. Ensure network access is allowed (for Atlas)
