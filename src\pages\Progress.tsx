import Navigation from '@/components/Navigation';
import StatsCard from '@/components/StatsCard';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress as ProgressBar } from '@/components/ui/progress';
import { 
  TrendingUp, 
  Target, 
  Zap, 
  Clock, 
  Calendar,
  Trophy,
  Flame
} from 'lucide-react';
import { currentUser, userJobs } from '@/data/mockData';

const Progress = () => {
  const completedJobs = userJobs.length;
  const totalPointsEarned = userJobs.reduce((sum, job) => sum + job.pointsEarned, 0);
  const averageAccuracy = userJobs.reduce((sum, job) => sum + job.accuracy, 0) / userJobs.length;
  
  // Mock data for charts - in real app this would come from API
  const weeklyData = [
    { day: 'Mon', points: 240, jobs: 3 },
    { day: 'Tue', points: 180, jobs: 2 },
    { day: 'Wed', points: 320, jobs: 4 },
    { day: 'Thu', points: 280, jobs: 3 },
    { day: 'Fri', points: 420, jobs: 5 },
    { day: 'Sat', points: 360, jobs: 4 },
    { day: 'Sun', points: 200, jobs: 2 },
  ];

  const levelProgress = ((currentUser.points % 500) / 500) * 100; // Assuming 500 points per level
  const pointsToNextLevel = 500 - (currentUser.points % 500);

  return (
    <div className="min-h-screen bg-background">
      <Navigation userRole="user" />
      
      <main className="ml-64 p-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-foreground mb-2">Your Progress</h1>
          <p className="text-muted-foreground">
            Track your typing performance and see how you're improving over time
          </p>
        </div>

        {/* Overview Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <StatsCard
            title="Jobs Completed"
            value={completedJobs}
            change={{ value: 25, type: 'increase' }}
            icon={<Target className="w-4 h-4 text-primary" />}
          />
          <StatsCard
            title="Total Points Earned"
            value={totalPointsEarned.toLocaleString()}
            change={{ value: 12, type: 'increase' }}
            icon={<Trophy className="w-4 h-4 text-warning" />}
          />
          <StatsCard
            title="Average Accuracy"
            value={`${Math.round(averageAccuracy)}%`}
            change={{ value: 3.2, type: 'increase' }}
            icon={<Zap className="w-4 h-4 text-accent" />}
          />
          <StatsCard
            title="Current Streak"
            value={`${currentUser.streak} days`}
            icon={<Flame className="w-4 h-4 text-destructive" />}
            badge={{ text: "Hot!", variant: "outline" }}
          />
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Level Progress */}
            <Card>
              <CardHeader>
                <CardTitle className="text-xl flex items-center gap-2">
                  <Trophy className="w-5 h-5 text-warning" />
                  Level Progress
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-2xl font-bold">Level {currentUser.level}</h3>
                      <p className="text-muted-foreground">Elite Typist</p>
                    </div>
                    <div className="text-right">
                      <div className="text-sm text-muted-foreground">Next Level</div>
                      <div className="font-bold">{pointsToNextLevel} points to go</div>
                    </div>
                  </div>
                  <ProgressBar value={levelProgress} className="h-3" />
                  <div className="flex justify-between text-sm text-muted-foreground">
                    <span>Level {currentUser.level}</span>
                    <span>Level {currentUser.level + 1}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Weekly Performance */}
            <Card>
              <CardHeader>
                <CardTitle className="text-xl flex items-center gap-2">
                  <TrendingUp className="w-5 h-5 text-accent" />
                  This Week's Performance
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {weeklyData.map((day, index) => (
                    <div key={day.day} className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center text-sm font-medium">
                          {day.day[0]}
                        </div>
                        <div>
                          <p className="font-medium">{day.day}</p>
                          <p className="text-sm text-muted-foreground">{day.jobs} jobs completed</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <Badge variant="secondary">+{day.points} pts</Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Recent Jobs */}
            <Card>
              <CardHeader>
                <CardTitle className="text-xl">Recent Job History</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {userJobs.map((job, index) => (
                    <div key={job.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-gradient-primary rounded-lg flex items-center justify-center text-white font-medium">
                          #{index + 1}
                        </div>
                        <div>
                          <p className="font-medium">Job Completed</p>
                          <div className="flex items-center gap-2 text-sm text-muted-foreground">
                            <Calendar className="w-3 h-3" />
                            {new Date(job.submittedAt).toLocaleDateString()}
                            <span>•</span>
                            <span>{job.accuracy}% accuracy</span>
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <Badge 
                          variant={job.status === 'approved' ? 'default' : 'secondary'}
                          className="mb-1"
                        >
                          {job.status}
                        </Badge>
                        <div className="text-sm font-bold">+{job.pointsEarned} pts</div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Personal Records */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Personal Records</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Zap className="w-4 h-4 text-accent" />
                    <span className="text-sm">Best Accuracy</span>
                  </div>
                  <span className="font-bold">100%</span>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Clock className="w-4 h-4 text-warning" />
                    <span className="text-sm">Fastest Job</span>
                  </div>
                  <span className="font-bold">3:45</span>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Target className="w-4 h-4 text-primary" />
                    <span className="text-sm">Most Points (Single Job)</span>
                  </div>
                  <span className="font-bold">148</span>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Flame className="w-4 h-4 text-destructive" />
                    <span className="text-sm">Longest Streak</span>
                  </div>
                  <span className="font-bold">15 days</span>
                </div>
              </CardContent>
            </Card>

            {/* Goals */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Current Goals</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span>Weekly Points Goal</span>
                    <span>1,680 / 2,000</span>
                  </div>
                  <ProgressBar value={84} className="h-2" />
                </div>
                
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span>Accuracy Target</span>
                    <span>94.5% / 95%</span>
                  </div>
                  <ProgressBar value={99.5} className="h-2" />
                </div>
                
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span>Daily Streak</span>
                    <span>{currentUser.streak} / 30 days</span>
                  </div>
                  <ProgressBar value={(currentUser.streak / 30) * 100} className="h-2" />
                </div>
              </CardContent>
            </Card>

            {/* Quick Tips */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Improvement Tips</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="text-sm text-muted-foreground">
                  💡 Your accuracy has improved 3.2% this week!
                </div>
                <div className="text-sm text-muted-foreground">
                  🎯 Focus on medium difficulty jobs to boost your level faster
                </div>
                <div className="text-sm text-muted-foreground">
                  ⚡ Try typing in short bursts to maintain concentration
                </div>
                <div className="text-sm text-muted-foreground">
                  🔥 Keep your streak alive - just {Math.ceil((30 - currentUser.streak) / 7)} more weeks to reach 30 days!
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  );
};

export default Progress;