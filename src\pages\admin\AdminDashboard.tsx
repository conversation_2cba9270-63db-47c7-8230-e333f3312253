import Navigation from '@/components/Navigation';
import StatsCard from '@/components/StatsCard';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Users, 
  FileText, 
  TrendingUp, 
  DollarSign,
  Clock,
  CheckCircle,
  XCircle,
  Plus
} from 'lucide-react';
import { dashboardStats, allUsers, mockJobs, userJobs } from '@/data/mockData';
import { Link } from 'react-router-dom';

const AdminDashboard = () => {
  const recentUsers = allUsers.slice(0, 5);
  const recentJobs = mockJobs.slice(0, 4);
  const pendingJobs = userJobs.filter(job => job.status === 'pending');

  return (
    <div className="min-h-screen bg-background">
      <Navigation userRole="admin" />
      
      <main className="ml-64 p-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-foreground mb-2">Admin Dashboard</h1>
          <p className="text-muted-foreground">
            Monitor platform performance and manage typing jobs
          </p>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <StatsCard
            title="Total Users"
            value={dashboardStats.totalUsers.toLocaleString()}
            change={{ value: 8, type: 'increase' }}
            icon={<Users className="w-4 h-4 text-primary" />}
          />
          <StatsCard
            title="Active Users"
            value={dashboardStats.activeUsers}
            badge={{ text: "Online", variant: "outline" }}
            icon={<Users className="w-4 h-4 text-accent" />}
          />
          <StatsCard
            title="Total Jobs"
            value={dashboardStats.totalJobs.toLocaleString()}
            change={{ value: 12, type: 'increase' }}
            icon={<FileText className="w-4 h-4 text-warning" />}
          />
          <StatsCard
            title="Avg Accuracy"
            value={`${dashboardStats.averageAccuracy}%`}
            change={{ value: 1.8, type: 'increase' }}
            icon={<TrendingUp className="w-4 h-4 text-accent" />}
          />
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Recent Jobs */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle className="text-xl">Recent Jobs</CardTitle>
                <Link to="/admin/jobs">
                  <Button size="sm">
                    <Plus className="w-4 h-4 mr-2" />
                    Add New Job
                  </Button>
                </Link>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentJobs.map((job) => (
                    <div key={job.id} className="flex items-center justify-between p-4 border rounded-lg hover:shadow-md transition-shadow">
                      <div className="flex-1">
                        <h3 className="font-semibold text-foreground">{job.title}</h3>
                        <div className="flex items-center gap-4 mt-2 text-sm text-muted-foreground">
                          <div className="flex items-center gap-1">
                            <Clock className="w-3 h-3" />
                            {job.timeLimit} min
                          </div>
                          <Badge 
                            variant="outline" 
                            className={
                              job.difficulty === 'easy' ? 'text-accent' : 
                              job.difficulty === 'medium' ? 'text-warning' : 
                              'text-destructive'
                            }
                          >
                            {job.difficulty}
                          </Badge>
                          <span>{job.points} points</span>
                        </div>
                      </div>
                      <div className="text-right">
                        <Badge variant="secondary">Available</Badge>
                        <p className="text-xs text-muted-foreground mt-1">
                          Created {new Date(job.createdAt).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* User Performance */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle className="text-xl">Top Performers</CardTitle>
                <Link to="/admin/users">
                  <Button variant="outline" size="sm">
                    View All Users
                  </Button>
                </Link>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentUsers.map((user, index) => (
                    <div key={user.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-gradient-primary rounded-full flex items-center justify-center text-white text-sm font-bold">
                          #{index + 1}
                        </div>
                        <div>
                          <p className="font-semibold">{user.name}</p>
                          <div className="flex items-center gap-2 text-sm text-muted-foreground">
                            <span>Level {user.level}</span>
                            <span>•</span>
                            <span>{user.accuracy}% accuracy</span>
                            <span>•</span>
                            <span>{user.streak} day streak</span>
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <Badge variant="secondary">{user.points.toLocaleString()} pts</Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Link to="/admin/jobs">
                  <Button className="w-full justify-start">
                    <Plus className="w-4 h-4 mr-2" />
                    Create New Job
                  </Button>
                </Link>
                
                <Link to="/admin/users">
                  <Button variant="outline" className="w-full justify-start">
                    <Users className="w-4 h-4 mr-2" />
                    Manage Users
                  </Button>
                </Link>
                
                <Link to="/admin/reports">
                  <Button variant="outline" className="w-full justify-start">
                    <TrendingUp className="w-4 h-4 mr-2" />
                    View Reports
                  </Button>
                </Link>
              </CardContent>
            </Card>

            {/* Pending Reviews */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  Pending Reviews
                  {pendingJobs.length > 0 && (
                    <Badge variant="destructive">{pendingJobs.length}</Badge>
                  )}
                </CardTitle>
              </CardHeader>
              <CardContent>
                {pendingJobs.length > 0 ? (
                  <div className="space-y-3">
                    {pendingJobs.slice(0, 3).map((job) => (
                      <div key={job.id} className="p-3 border rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <p className="font-medium text-sm">Job Submission</p>
                          <Badge variant="outline">Pending</Badge>
                        </div>
                        <p className="text-xs text-muted-foreground mb-2">
                          {job.accuracy}% accuracy • Submitted {new Date(job.submittedAt).toLocaleDateString()}
                        </p>
                        <div className="flex gap-2">
                          <Button size="sm" variant="outline" className="text-accent border-accent">
                            <CheckCircle className="w-3 h-3 mr-1" />
                            Approve
                          </Button>
                          <Button size="sm" variant="outline" className="text-destructive border-destructive">
                            <XCircle className="w-3 h-3 mr-1" />
                            Reject
                          </Button>
                        </div>
                      </div>
                    ))}
                    {pendingJobs.length > 3 && (
                      <Button variant="ghost" size="sm" className="w-full">
                        View All Pending ({pendingJobs.length})
                      </Button>
                    )}
                  </div>
                ) : (
                  <div className="text-center py-6">
                    <CheckCircle className="w-8 h-8 text-accent mx-auto mb-2" />
                    <p className="text-sm text-muted-foreground">All caught up!</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Platform Stats */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Platform Metrics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Completed Jobs</span>
                  <span className="font-bold">{dashboardStats.completedJobs.toLocaleString()}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Points Awarded</span>
                  <span className="font-bold">{dashboardStats.totalPointsAwarded.toLocaleString()}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Success Rate</span>
                  <span className="font-bold">97.3%</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Avg Job Time</span>
                  <span className="font-bold">8.5 min</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  );
};

export default AdminDashboard;