import Navigation from '@/components/Navigation';
import StatsCard from '@/components/StatsCard';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  TrendingUp, 
  Users, 
  FileText, 
  Target,
  Download,
  Calendar,
  BarChart3,
  PieChart
} from 'lucide-react';
import { dashboardStats } from '@/data/mockData';
import { useState } from 'react';

const Reports = () => {
  const [timeRange, setTimeRange] = useState('7days');
  const [reportType, setReportType] = useState('overview');

  // Mock data for charts
  const weeklyStats = [
    { day: 'Mon', users: 45, jobs: 120, accuracy: 91.2 },
    { day: 'Tue', users: 52, jobs: 135, accuracy: 92.8 },
    { day: 'Wed', users: 48, jobs: 128, accuracy: 90.5 },
    { day: 'Thu', users: 61, jobs: 142, accuracy: 93.1 },
    { day: 'Fri', users: 68, jobs: 156, accuracy: 94.2 },
    { day: 'Sat', users: 34, jobs: 89, accuracy: 89.7 },
    { day: 'Sun', users: 29, jobs: 76, accuracy: 88.9 },
  ];

  const performanceMetrics = {
    totalRevenue: '$12,450',
    avgJobCompletion: '8.5 min',
    userRetention: '87%',
    platformUptime: '99.9%'
  };

  const topPerformers = [
    { name: 'Emily Davis', level: 15, accuracy: 97.2, jobs: 34 },
    { name: 'Sarah Chen', level: 12, accuracy: 96.8, jobs: 28 },
    { name: 'Alex Johnson', level: 8, accuracy: 94.5, jobs: 23 },
    { name: 'Mike Rodriguez', level: 6, accuracy: 89.3, jobs: 19 },
  ];

  const exportReport = () => {
    console.log('Exporting report...');
  };

  return (
    <div className="min-h-screen bg-background">
      <Navigation userRole="admin" />
      
      <main className="ml-64 p-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-foreground mb-2">Reports & Analytics</h1>
            <p className="text-muted-foreground">
              Comprehensive insights into platform performance and user engagement
            </p>
          </div>
          
          <div className="flex gap-3">
            <Select value={timeRange} onValueChange={setTimeRange}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7days">Last 7 days</SelectItem>
                <SelectItem value="30days">Last 30 days</SelectItem>
                <SelectItem value="90days">Last 90 days</SelectItem>
                <SelectItem value="1year">Last year</SelectItem>
              </SelectContent>
            </Select>
            
            <Button onClick={exportReport}>
              <Download className="w-4 h-4 mr-2" />
              Export Report
            </Button>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <StatsCard
            title="Platform Revenue"
            value={performanceMetrics.totalRevenue}
            change={{ value: 15, type: 'increase' }}
            icon={<Target className="w-4 h-4 text-primary" />}
          />
          <StatsCard
            title="Active Users"
            value={dashboardStats.activeUsers}
            change={{ value: 8, type: 'increase' }}
            icon={<Users className="w-4 h-4 text-accent" />}
          />
          <StatsCard
            title="Jobs Completed"
            value={dashboardStats.completedJobs.toLocaleString()}
            change={{ value: 23, type: 'increase' }}
            icon={<FileText className="w-4 h-4 text-warning" />}
          />
          <StatsCard
            title="User Retention"
            value={performanceMetrics.userRetention}
            change={{ value: 3, type: 'increase' }}
            icon={<TrendingUp className="w-4 h-4 text-accent" />}
          />
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Charts */}
          <div className="lg:col-span-2 space-y-6">
            {/* Weekly Performance */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle className="text-xl flex items-center gap-2">
                  <BarChart3 className="w-5 h-5" />
                  Weekly Performance Trends
                </CardTitle>
                <Select value={reportType} onValueChange={setReportType}>
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="overview">Overview</SelectItem>
                    <SelectItem value="users">Users</SelectItem>
                    <SelectItem value="jobs">Jobs</SelectItem>
                    <SelectItem value="accuracy">Accuracy</SelectItem>
                  </SelectContent>
                </Select>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {weeklyStats.map((stat, index) => (
                    <div key={stat.day} className="flex items-center justify-between p-4 bg-muted/50 rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center text-sm font-medium">
                          {stat.day[0]}
                        </div>
                        <div>
                          <p className="font-medium">{stat.day}</p>
                          <p className="text-sm text-muted-foreground">{stat.users} active users</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-bold">{stat.jobs} jobs</div>
                        <div className="text-sm text-muted-foreground">{stat.accuracy}% accuracy</div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Detailed Metrics */}
            <Card>
              <CardHeader>
                <CardTitle className="text-xl">Detailed Platform Metrics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Average Job Completion Time</span>
                      <span className="font-bold">{performanceMetrics.avgJobCompletion}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Platform Uptime</span>
                      <Badge variant="outline" className="text-accent border-accent">
                        {performanceMetrics.platformUptime}
                      </Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">New User Signups</span>
                      <span className="font-bold">+47 this week</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Total Points Awarded</span>
                      <span className="font-bold">{dashboardStats.totalPointsAwarded.toLocaleString()}</span>
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Job Success Rate</span>
                      <span className="font-bold">97.3%</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Average Accuracy</span>
                      <span className="font-bold">{dashboardStats.averageAccuracy}%</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Daily Active Users</span>
                      <span className="font-bold">89</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Revenue per User</span>
                      <span className="font-bold">$9.98</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Top Performers */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <PieChart className="w-5 h-5" />
                  Top Performers
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {topPerformers.map((user, index) => (
                  <div key={user.name} className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="w-6 h-6 bg-gradient-primary rounded-full flex items-center justify-center text-white text-xs font-bold">
                        #{index + 1}
                      </div>
                      <div>
                        <p className="font-medium text-sm">{user.name}</p>
                        <p className="text-xs text-muted-foreground">
                          Lv.{user.level} • {user.accuracy}% acc
                        </p>
                      </div>
                    </div>
                    <Badge variant="secondary">{user.jobs} jobs</Badge>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Quick Reports</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button variant="outline" className="w-full justify-start">
                  <Calendar className="w-4 h-4 mr-2" />
                  Monthly Summary
                </Button>
                
                <Button variant="outline" className="w-full justify-start">
                  <Users className="w-4 h-4 mr-2" />
                  User Performance
                </Button>
                
                <Button variant="outline" className="w-full justify-start">
                  <FileText className="w-4 h-4 mr-2" />
                  Job Analytics
                </Button>
                
                <Button variant="outline" className="w-full justify-start">
                  <Target className="w-4 h-4 mr-2" />
                  Revenue Report
                </Button>
              </CardContent>
            </Card>

            {/* System Health */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">System Health</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm">Server Response</span>
                  <Badge variant="outline" className="text-accent">
                    145ms
                  </Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Database Health</span>
                  <Badge variant="outline" className="text-accent">
                    Excellent
                  </Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">API Uptime</span>
                  <Badge variant="outline" className="text-accent">
                    99.9%
                  </Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Active Sessions</span>
                  <span className="font-bold">127</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  );
};

export default Reports;