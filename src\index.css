@tailwind base;
@tailwind components;
@tailwind utilities;

/* Typing Job Platform Design System */

@layer base {
  :root {
    /* Core Brand Colors */
    --background: 0 0% 100%;
    --foreground: 224 15% 15%;

    /* Cards & Surfaces */
    --card: 0 0% 100%;
    --card-foreground: 224 15% 15%;
    --card-elevated: 240 10% 98%;

    --popover: 0 0% 100%;
    --popover-foreground: 224 15% 15%;

    /* Primary Brand (Deep Purple/Blue) */
    --primary: 245 60% 55%;
    --primary-foreground: 0 0% 100%;
    --primary-light: 245 60% 65%;
    --primary-dark: 245 60% 45%;

    /* Secondary (Productivity Gray) */
    --secondary: 240 10% 96%;
    --secondary-foreground: 224 15% 25%;

    /* Muted & Text */
    --muted: 240 10% 96%;
    --muted-foreground: 224 8% 50%;

    /* Accent (Success Green) */
    --accent: 142 76% 36%;
    --accent-foreground: 0 0% 100%;
    --accent-light: 142 76% 46%;

    /* Progress Orange */
    --warning: 35 91% 56%;
    --warning-foreground: 0 0% 100%;

    /* Error States */
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    /* UI Elements */
    --border: 240 6% 90%;
    --input: 240 6% 96%;
    --ring: 245 60% 55%;

    /* Typography Scale */
    --font-sans: 'Inter', system-ui, sans-serif;
    --font-mono: 'JetBrains Mono', 'Monaco', monospace;

    /* Spacing & Layout */
    --radius: 0.75rem;
    --radius-sm: 0.5rem;
    --radius-lg: 1rem;

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(245 60% 55%) 0%, hsl(260 60% 45%) 100%);
    --gradient-success: linear-gradient(135deg, hsl(142 76% 36%) 0%, hsl(158 76% 46%) 100%);
    --gradient-hero: linear-gradient(135deg, hsl(245 60% 55%) 0%, hsl(260 60% 45%) 50%, hsl(142 76% 36%) 100%);

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 hsl(240 6% 90% / 0.1);
    --shadow-md: 0 4px 6px -1px hsl(240 6% 90% / 0.1), 0 2px 4px -1px hsl(240 6% 90% / 0.06);
    --shadow-lg: 0 10px 15px -3px hsl(240 6% 90% / 0.1), 0 4px 6px -2px hsl(240 6% 90% / 0.05);
    --shadow-glow: 0 0 20px hsl(245 60% 55% / 0.3);

    /* Animations */
    --transition-smooth: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}
