import { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  LayoutDashboard, 
  FileText, 
  TrendingUp, 
  User, 
  Trophy,
  Settings,
  LogOut,
  Zap
} from 'lucide-react';
import { currentUser } from '@/data/mockData';

interface NavigationProps {
  userRole?: 'user' | 'admin';
}

const Navigation = ({ userRole = 'user' }: NavigationProps) => {
  const location = useLocation();
  const [isCollapsed, setIsCollapsed] = useState(false);

  const userNavItems = [
    { href: '/dashboard', label: 'Dashboard', icon: LayoutDashboard },
    { href: '/jobs', label: 'Jobs', icon: FileText },
    { href: '/progress', label: 'Progress', icon: TrendingUp },
    { href: '/achievements', label: 'Achievements', icon: Trophy },
    { href: '/profile', label: 'Profile', icon: User },
  ];

  const adminNavItems = [
    { href: '/admin', label: 'Dashboard', icon: LayoutDashboard },
    { href: '/admin/jobs', label: 'Manage Jobs', icon: FileText },
    { href: '/admin/users', label: 'Users', icon: User },
    { href: '/admin/reports', label: 'Reports', icon: TrendingUp },
    { href: '/admin/settings', label: 'Settings', icon: Settings },
  ];

  const navItems = userRole === 'admin' ? adminNavItems : userNavItems;

  return (
    <div className={`fixed left-0 top-0 h-full bg-card border-r border-border transition-all duration-300 ${
      isCollapsed ? 'w-16' : 'w-64'
    } z-10`}>
      {/* Header */}
      <div className="p-6 border-b border-border">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center">
            <Zap className="w-5 h-5 text-white" />
          </div>
          {!isCollapsed && (
            <div>
              <h1 className="text-lg font-bold text-foreground">TypePro</h1>
              <p className="text-xs text-muted-foreground">Typing Platform</p>
            </div>
          )}
        </div>
      </div>

      {/* User Info */}
      {userRole === 'user' && (
        <div className="p-4 border-b border-border">
          {!isCollapsed ? (
            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gradient-primary rounded-full flex items-center justify-center text-white font-medium">
                  {currentUser.name.split(' ').map(n => n[0]).join('')}
                </div>
                <div className="flex-1">
                  <p className="font-medium text-sm text-foreground">{currentUser.name}</p>
                  <p className="text-xs text-muted-foreground">Level {currentUser.level}</p>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <Badge variant="secondary" className="text-xs">
                  {currentUser.points.toLocaleString()} pts
                </Badge>
                <Badge variant="outline" className="text-xs">
                  {currentUser.accuracy}% acc
                </Badge>
              </div>
            </div>
          ) : (
            <div className="w-8 h-8 bg-gradient-primary rounded-full flex items-center justify-center text-white font-medium mx-auto">
              {currentUser.name.split(' ').map(n => n[0]).join('')}
            </div>
          )}
        </div>
      )}

      {/* Navigation Items */}
      <nav className="p-4 space-y-2 flex-1">
        {navItems.map((item) => {
          const isActive = location.pathname === item.href;
          const Icon = item.icon;
          
          return (
            <Link key={item.href} to={item.href}>
              <Button
                variant={isActive ? "default" : "ghost"}
                className={`w-full justify-start h-10 ${
                  isCollapsed ? 'px-2' : 'px-3'
                } ${isActive ? 'bg-primary text-primary-foreground' : 'hover:bg-muted'}`}
              >
                <Icon className="w-4 h-4" />
                {!isCollapsed && <span className="ml-3">{item.label}</span>}
              </Button>
            </Link>
          );
        })}
      </nav>

      {/* Footer */}
      <div className="p-4 border-t border-border">
        <Button
          variant="ghost"
          className={`w-full justify-start ${isCollapsed ? 'px-2' : 'px-3'}`}
        >
          <LogOut className="w-4 h-4" />
          {!isCollapsed && <span className="ml-3">Logout</span>}
        </Button>
      </div>
    </div>
  );
};

export default Navigation;