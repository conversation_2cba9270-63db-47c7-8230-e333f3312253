import { Link } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Zap, 
  Target, 
  Trophy, 
  Users, 
  CheckCircle, 
  ArrowRight,
  Star,
  Clock
} from 'lucide-react';

const Index = () => {
  const features = [
    {
      icon: <Target className="w-6 h-6 text-primary" />,
      title: "Earn Points",
      description: "Complete typing jobs and earn points based on your accuracy and speed"
    },
    {
      icon: <Trophy className="w-6 h-6 text-warning" />,
      title: "Level Up",
      description: "Progress through levels and unlock achievements as you improve"
    },
    {
      icon: <Zap className="w-6 h-6 text-accent" />,
      title: "Track Progress",
      description: "Monitor your typing accuracy, speed, and overall performance"
    },
    {
      icon: <Users className="w-6 h-6 text-primary" />,
      title: "Join Community",
      description: "Compete with other typists and climb the leaderboards"
    }
  ];

  const stats = [
    { label: "Active Users", value: "1,247" },
    { label: "Jobs Completed", value: "2,891" },
    { label: "Average Accuracy", value: "91.2%" },
    { label: "Points Awarded", value: "445K" }
  ];

  return (
    <div className="min-h-screen bg-background">
      {/* Navigation */}
      <nav className="border-b border-border bg-card/50 backdrop-blur">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center">
                <Zap className="w-5 h-5 text-white" />
              </div>
              <span className="text-xl font-bold text-foreground">TypePro</span>
            </div>
            
            <div className="flex items-center gap-4">
              <Link to="/dashboard">
                <Button variant="ghost">Dashboard</Button>
              </Link>
              <Link to="/admin">
                <Button variant="outline">Admin</Button>
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="relative py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto text-center">
          <div className="mb-8">
            <Badge variant="outline" className="mb-4">
              <Star className="w-3 h-3 mr-1" />
              Professional Typing Platform
            </Badge>
            <h1 className="text-4xl md:text-6xl font-bold text-foreground mb-6">
              Master Your Typing Skills
              <span className="bg-gradient-primary bg-clip-text text-transparent block mt-2">
                Earn While You Learn
              </span>
            </h1>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto mb-8">
              Join thousands of typists improving their skills through engaging jobs. 
              Earn points, unlock achievements, and track your progress in real-time.
            </p>
          </div>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-16">
            <Link to="/dashboard">
              <Button size="lg" className="min-w-48">
                Start Typing Jobs
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            </Link>
            <Link to="/admin">
              <Button variant="outline" size="lg" className="min-w-48">
                Admin Dashboard
              </Button>
            </Link>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto">
            {stats.map((stat) => (
              <div key={stat.label} className="text-center">
                <div className="text-3xl font-bold text-foreground mb-1">{stat.value}</div>
                <div className="text-sm text-muted-foreground">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-muted/30">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
              Why Choose TypePro?
            </h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Our platform combines gamification with professional development to make typing practice engaging and rewarding.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <Card key={index} className="text-center hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                    {feature.icon}
                  </div>
                  <CardTitle className="text-xl">{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">{feature.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
              How It Works
            </h2>
            <p className="text-xl text-muted-foreground">
              Get started in three simple steps
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center text-primary-foreground text-2xl font-bold mx-auto mb-6">
                1
              </div>
              <h3 className="text-xl font-semibold mb-4">Choose a Job</h3>
              <p className="text-muted-foreground">
                Browse available typing jobs with different difficulty levels and point rewards
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-accent rounded-full flex items-center justify-center text-accent-foreground text-2xl font-bold mx-auto mb-6">
                2
              </div>
              <h3 className="text-xl font-semibold mb-4">Type Accurately</h3>
              <p className="text-muted-foreground">
                Transcribe the provided text with focus on accuracy and speed
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-warning rounded-full flex items-center justify-center text-warning-foreground text-2xl font-bold mx-auto mb-6">
                3
              </div>
              <h3 className="text-xl font-semibold mb-4">Earn Rewards</h3>
              <p className="text-muted-foreground">
                Gain points, level up, and unlock achievements based on your performance
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-primary text-white">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Ready to Start Your Typing Journey?
          </h2>
          <p className="text-xl mb-8 opacity-90">
            Join our community of professional typists and start earning points today
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link to="/dashboard">
              <Button size="lg" variant="secondary" className="min-w-48">
                <Zap className="w-4 h-4 mr-2" />
                Start Now
              </Button>
            </Link>
            <Link to="/jobs">
              <Button size="lg" variant="outline" className="min-w-48 border-white text-white hover:bg-white hover:text-primary">
                <Clock className="w-4 h-4 mr-2" />
                View Jobs
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-card border-t border-border py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center gap-3 mb-4 md:mb-0">
              <div className="w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center">
                <Zap className="w-5 h-5 text-white" />
              </div>
              <span className="text-xl font-bold text-foreground">TypePro</span>
            </div>
            
            <div className="flex items-center gap-6 text-sm text-muted-foreground">
              <Link to="/dashboard" className="hover:text-foreground transition-colors">
                Dashboard
              </Link>
              <Link to="/jobs" className="hover:text-foreground transition-colors">
                Jobs
              </Link>
              <Link to="/admin" className="hover:text-foreground transition-colors">
                Admin
              </Link>
            </div>
          </div>
          
          <div className="border-t border-border mt-8 pt-8 text-center text-sm text-muted-foreground">
            <p>&copy; 2024 TypePro. Professional typing platform for skill development.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Index;
