import Navigation from '@/components/Navigation';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Trophy, Star, Target, Lock } from 'lucide-react';
import { badges } from '@/data/mockData';

const Achievements = () => {
  const earnedBadges = badges.filter(b => b.earned);
  const lockedBadges = badges.filter(b => !b.earned);

  return (
    <div className="min-h-screen bg-background">
      <Navigation userRole="user" />
      
      <main className="ml-64 p-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-foreground mb-2">Achievements</h1>
          <p className="text-muted-foreground">
            Unlock badges and rewards as you improve your typing skills
          </p>
        </div>

        {/* Progress Overview */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="text-xl flex items-center gap-2">
              <Trophy className="w-5 h-5 text-warning" />
              Achievement Progress
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="text-3xl font-bold text-primary mb-1">{earnedBadges.length}</div>
                <div className="text-sm text-muted-foreground">Badges Earned</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-muted-foreground mb-1">{lockedBadges.length}</div>
                <div className="text-sm text-muted-foreground">Still Locked</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-accent mb-1">{Math.round((earnedBadges.length / badges.length) * 100)}%</div>
                <div className="text-sm text-muted-foreground">Completion</div>
              </div>
            </div>
            <Progress value={(earnedBadges.length / badges.length) * 100} className="mt-6" />
          </CardContent>
        </Card>

        {/* Earned Badges */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold mb-4 flex items-center gap-2">
            <Star className="w-6 h-6 text-warning" />
            Earned Achievements
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {earnedBadges.map((badge) => (
              <Card key={badge.id} className="relative overflow-hidden border-accent/20 bg-accent/5">
                <div className="absolute top-2 right-2">
                  <Badge variant="default" className="bg-accent text-accent-foreground">
                    Earned
                  </Badge>
                </div>
                <CardHeader className="pb-4">
                  <div className="flex items-center gap-3">
                    <div className="text-4xl">{badge.icon}</div>
                    <div>
                      <CardTitle className="text-lg">{badge.name}</CardTitle>
                      <div className="text-sm text-muted-foreground">
                        Earned {badge.earnedAt ? new Date(badge.earnedAt).toLocaleDateString() : 'Recently'}
                      </div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground mb-2">{badge.description}</p>
                  <div className="text-xs text-accent font-medium">✓ {badge.requirement}</div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Locked Badges */}
        <div>
          <h2 className="text-2xl font-bold mb-4 flex items-center gap-2">
            <Target className="w-6 h-6 text-muted-foreground" />
            Locked Achievements
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {lockedBadges.map((badge) => (
              <Card key={badge.id} className="relative overflow-hidden border-muted bg-muted/20 opacity-75">
                <div className="absolute top-2 right-2">
                  <Badge variant="secondary" className="bg-muted">
                    <Lock className="w-3 h-3 mr-1" />
                    Locked
                  </Badge>
                </div>
                <CardHeader className="pb-4">
                  <div className="flex items-center gap-3">
                    <div className="text-4xl grayscale">{badge.icon}</div>
                    <div>
                      <CardTitle className="text-lg text-muted-foreground">{badge.name}</CardTitle>
                      <div className="text-sm text-muted-foreground">Not earned yet</div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground mb-2">{badge.description}</p>
                  <div className="text-xs text-muted-foreground">📋 {badge.requirement}</div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Tips */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle className="text-lg">How to Earn More Badges</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <h4 className="font-medium mb-2">📈 Improve Your Skills</h4>
                <p className="text-muted-foreground">
                  Complete jobs regularly and focus on maintaining high accuracy to unlock skill-based achievements.
                </p>
              </div>
              <div>
                <h4 className="font-medium mb-2">🔥 Maintain Streaks</h4>
                <p className="text-muted-foreground">
                  Login and complete at least one job daily to build up your consistency streaks.
                </p>
              </div>
              <div>
                <h4 className="font-medium mb-2">⚡ Speed Challenges</h4>
                <p className="text-muted-foreground">
                  Work on your typing speed while maintaining accuracy to earn speed-related badges.
                </p>
              </div>
              <div>
                <h4 className="font-medium mb-2">🎯 Special Goals</h4>
                <p className="text-muted-foreground">
                  Some achievements require specific milestones like reaching certain levels or point totals.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </main>
    </div>
  );
};

export default Achievements;