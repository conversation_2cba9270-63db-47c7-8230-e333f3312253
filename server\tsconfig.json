{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "outDir": "../dist/server", "rootDir": ".", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": false, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "sourceMap": true}, "include": ["**/*"], "exclude": ["node_modules", "../dist"]}