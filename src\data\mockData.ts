import { User, Job, UserJob, Badge, DashboardStats } from '@/types';

export const currentUser: User = {
  id: 'user-1',
  name: '<PERSON>',
  email: '<EMAIL>',
  role: 'user',
  points: 2450,
  accuracy: 94.5,
  level: 8,
  streak: 12,
  joinedAt: '2024-01-15'
};

export const mockJobs: Job[] = [
  {
    id: 'job-1',
    title: 'Medical Report Transcription',
    imageUrl: '/api/placeholder/600/400',
    correctText: 'Pat<PERSON> presents with acute myocardial infarction. Recommended immediate intervention with cardiac catheterization.',
    points: 150,
    difficulty: 'hard',
    timeLimit: 15,
    status: 'available',
    createdAt: '2024-03-08'
  },
  {
    id: 'job-2',
    title: 'Legal Document Typing',
    imageUrl: '/api/placeholder/600/400',
    correctText: 'The defendant hereby agrees to the terms and conditions set forth in this settlement agreement.',
    points: 120,
    difficulty: 'medium',
    timeLimit: 10,
    status: 'available',
    createdAt: '2024-03-08'
  },
  {
    id: 'job-3',
    title: 'Product Description',
    imageUrl: '/api/placeholder/600/400',
    correctText: 'Premium quality cotton t-shirt with comfortable fit and durable construction. Available in multiple colors.',
    points: 80,
    difficulty: 'easy',
    timeLimit: 8,
    status: 'available',
    createdAt: '2024-03-08'
  }
];

export const userJobs: UserJob[] = [
  {
    id: 'uj-1',
    userId: 'user-1',
    jobId: 'job-1',
    submittedText: 'Patient presents with acute myocardial infarction. Recommended immediate intervention with cardiac catheterization.',
    accuracy: 98.5,
    status: 'approved',
    submittedAt: '2024-03-07T10:30:00Z',
    reviewedAt: '2024-03-07T14:20:00Z',
    pointsEarned: 148
  },
  {
    id: 'uj-2',
    userId: 'user-1',
    jobId: 'job-2',
    submittedText: 'The defendant hereby agrees to the terms and conditions set forth in this settlement agreement.',
    accuracy: 100,
    status: 'approved',
    submittedAt: '2024-03-06T15:45:00Z',
    reviewedAt: '2024-03-06T16:30:00Z',
    pointsEarned: 120
  }
];

export const badges: Badge[] = [
  {
    id: 'badge-1',
    name: 'Speed Demon',
    description: 'Complete 5 jobs in under 5 minutes each',
    icon: '⚡',
    requirement: '5 fast completions',
    earned: true,
    earnedAt: '2024-03-01'
  },
  {
    id: 'badge-2',
    name: 'Accuracy Master',
    description: 'Maintain 95%+ accuracy for 10 consecutive jobs',
    icon: '🎯',
    requirement: '95%+ accuracy streak',
    earned: true,
    earnedAt: '2024-03-05'
  },
  {
    id: 'badge-3',
    name: 'Dedication',
    description: 'Complete jobs for 7 consecutive days',
    icon: '🔥',
    requirement: '7-day streak',
    earned: false
  },
  {
    id: 'badge-4',
    name: 'Elite Typist',
    description: 'Reach level 10 and maintain 98%+ accuracy',
    icon: '👑',
    requirement: 'Level 10 + 98% accuracy',
    earned: false
  }
];

export const dashboardStats: DashboardStats = {
  totalUsers: 1247,
  activeUsers: 89,
  totalJobs: 3456,
  completedJobs: 2891,
  averageAccuracy: 91.2,
  totalPointsAwarded: 445670
};

export const allUsers: User[] = [
  currentUser,
  {
    id: 'user-2',
    name: 'Sarah Chen',
    email: '<EMAIL>',
    role: 'user',
    points: 3240,
    accuracy: 96.8,
    level: 12,
    streak: 8,
    joinedAt: '2024-01-10'
  },
  {
    id: 'user-3',
    name: 'Mike Rodriguez',
    email: '<EMAIL>',
    role: 'user',
    points: 1890,
    accuracy: 89.3,
    level: 6,
    streak: 3,
    joinedAt: '2024-02-20'
  },
  {
    id: 'user-4',
    name: 'Emily Davis',
    email: '<EMAIL>',
    role: 'user',
    points: 4100,
    accuracy: 97.2,
    level: 15,
    streak: 25,
    joinedAt: '2023-12-05'
  }
];