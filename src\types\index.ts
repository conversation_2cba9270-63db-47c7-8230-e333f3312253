export interface User {
  id: string;
  name: string;
  email: string;
  password?: string; // Optional for frontend, required for backend
  role: 'user' | 'admin';
  points: number;
  accuracy: number;
  level: number;
  streak: number;
  joinedAt: string;
  isVerified?: boolean;
  lastLogin?: string;
}

export interface AuthUser {
  id: string;
  name: string;
  email: string;
  role: 'user' | 'admin';
  points: number;
  accuracy: number;
  level: number;
  streak: number;
  joinedAt: string;
  isVerified: boolean;
  lastLogin?: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface SignupCredentials {
  name: string;
  email: string;
  password: string;
  confirmPassword: string;
}

export interface AuthResponse {
  success: boolean;
  message: string;
  user?: AuthUser;
  token?: string;
}

export interface AuthContextType {
  user: AuthUser | null;
  token: string | null;
  login: (credentials: LoginCredentials) => Promise<AuthResponse>;
  signup: (credentials: SignupCredentials) => Promise<AuthResponse>;
  logout: () => void;
  isLoading: boolean;
  isAuthenticated: boolean;
}

export interface Job {
  id: string;
  title: string;
  imageUrl: string;
  correctText: string;
  points: number;
  difficulty: 'easy' | 'medium' | 'hard';
  timeLimit: number; // in minutes
  status: 'available' | 'in_progress' | 'completed';
  createdAt: string;
}

export interface UserJob {
  id: string;
  userId: string;
  jobId: string;
  submittedText: string;
  accuracy: number;
  status: 'pending' | 'approved' | 'rejected';
  submittedAt: string;
  reviewedAt?: string;
  pointsEarned: number;
}

export interface Badge {
  id: string;
  name: string;
  description: string;
  icon: string;
  requirement: string;
  earned: boolean;
  earnedAt?: string;
}

export interface DashboardStats {
  totalUsers: number;
  activeUsers: number;
  totalJobs: number;
  completedJobs: number;
  averageAccuracy: number;
  totalPointsAwarded: number;
}