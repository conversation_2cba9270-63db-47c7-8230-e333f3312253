export interface User {
  id: string;
  name: string;
  email: string;
  role: 'user' | 'admin';
  points: number;
  accuracy: number;
  level: number;
  streak: number;
  joinedAt: string;
}

export interface Job {
  id: string;
  title: string;
  imageUrl: string;
  correctText: string;
  points: number;
  difficulty: 'easy' | 'medium' | 'hard';
  timeLimit: number; // in minutes
  status: 'available' | 'in_progress' | 'completed';
  createdAt: string;
}

export interface UserJob {
  id: string;
  userId: string;
  jobId: string;
  submittedText: string;
  accuracy: number;
  status: 'pending' | 'approved' | 'rejected';
  submittedAt: string;
  reviewedAt?: string;
  pointsEarned: number;
}

export interface Badge {
  id: string;
  name: string;
  description: string;
  icon: string;
  requirement: string;
  earned: boolean;
  earnedAt?: string;
}

export interface DashboardStats {
  totalUsers: number;
  activeUsers: number;
  totalJobs: number;
  completedJobs: number;
  averageAccuracy: number;
  totalPointsAwarded: number;
}