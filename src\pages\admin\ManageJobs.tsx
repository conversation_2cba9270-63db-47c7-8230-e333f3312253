import Navigation from '@/components/Navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Plus, Edit, Trash2, Upload, Clock, Target } from 'lucide-react';
import { mockJobs } from '@/data/mockData';
import { useState } from 'react';

const ManageJobs = () => {
  const [jobs, setJobs] = useState(mockJobs);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [newJob, setNewJob] = useState({
    title: '',
    correctText: '',
    points: '',
    difficulty: 'medium' as 'easy' | 'medium' | 'hard',
    timeLimit: ''
  });

  const handleCreateJob = () => {
    const job = {
      id: `job-${Date.now()}`,
      title: newJob.title,
      imageUrl: '/api/placeholder/600/400',
      correctText: newJob.correctText,
      points: parseInt(newJob.points),
      difficulty: newJob.difficulty,
      timeLimit: parseInt(newJob.timeLimit),
      status: 'available' as const,
      createdAt: new Date().toISOString().split('T')[0]
    };

    setJobs([job, ...jobs]);
    setIsCreateDialogOpen(false);
    setNewJob({
      title: '',
      correctText: '',
      points: '',
      difficulty: 'medium',
      timeLimit: ''
    });
  };

  const handleDeleteJob = (jobId: string) => {
    setJobs(jobs.filter(job => job.id !== jobId));
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'text-accent border-accent/20 bg-accent/10';
      case 'medium': return 'text-warning border-warning/20 bg-warning/10';
      case 'hard': return 'text-destructive border-destructive/20 bg-destructive/10';
      default: return '';
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <Navigation userRole="admin" />
      
      <main className="ml-64 p-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-foreground mb-2">Manage Jobs</h1>
            <p className="text-muted-foreground">
              Create and manage typing jobs for your users
            </p>
          </div>
          
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="w-4 h-4 mr-2" />
                Create New Job
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Create New Typing Job</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="title">Job Title</Label>
                  <Input
                    id="title"
                    placeholder="e.g., Medical Report Transcription"
                    value={newJob.title}
                    onChange={(e) => setNewJob({...newJob, title: e.target.value})}
                  />
                </div>
                
                <div>
                  <Label htmlFor="text">Text to Transcribe</Label>
                  <Textarea
                    id="text"
                    placeholder="Enter the text that users will need to type..."
                    className="min-h-[120px]"
                    value={newJob.correctText}
                    onChange={(e) => setNewJob({...newJob, correctText: e.target.value})}
                  />
                </div>
                
                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="points">Points Reward</Label>
                    <Input
                      id="points"
                      type="number"
                      placeholder="100"
                      value={newJob.points}
                      onChange={(e) => setNewJob({...newJob, points: e.target.value})}
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="difficulty">Difficulty</Label>
                    <Select value={newJob.difficulty} onValueChange={(value: 'easy' | 'medium' | 'hard') => setNewJob({...newJob, difficulty: value})}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="easy">Easy</SelectItem>
                        <SelectItem value="medium">Medium</SelectItem>
                        <SelectItem value="hard">Hard</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div>
                    <Label htmlFor="timeLimit">Time Limit (minutes)</Label>
                    <Input
                      id="timeLimit"
                      type="number"
                      placeholder="10"
                      value={newJob.timeLimit}
                      onChange={(e) => setNewJob({...newJob, timeLimit: e.target.value})}
                    />
                  </div>
                </div>
                
                <div>
                  <Label>Image Upload (Optional)</Label>
                  <div className="border-2 border-dashed border-muted rounded-lg p-8 text-center">
                    <Upload className="w-8 h-8 text-muted-foreground mx-auto mb-2" />
                    <p className="text-sm text-muted-foreground">
                      Click to upload an image or drag and drop
                    </p>
                    <p className="text-xs text-muted-foreground mt-1">
                      PNG, JPG up to 10MB
                    </p>
                  </div>
                </div>
                
                <div className="flex gap-3 pt-4">
                  <Button onClick={handleCreateJob} disabled={!newJob.title || !newJob.correctText}>
                    Create Job
                  </Button>
                  <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                    Cancel
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>

        {/* Jobs List */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {jobs.map((job) => (
            <Card key={job.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="text-lg mb-2">{job.title}</CardTitle>
                    <div className="flex items-center gap-2 mb-3">
                      <Badge 
                        variant="outline" 
                        className={getDifficultyColor(job.difficulty)}
                      >
                        {job.difficulty.toUpperCase()}
                      </Badge>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-xl font-bold text-primary">{job.points}</div>
                    <div className="text-xs text-muted-foreground">points</div>
                  </div>
                </div>
              </CardHeader>
              
              <CardContent>
                <div className="space-y-4">
                  {/* Job Details */}
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div className="flex items-center gap-2">
                      <Clock className="w-4 h-4 text-muted-foreground" />
                      <span>{job.timeLimit} min</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Target className="w-4 h-4 text-muted-foreground" />
                      <span>Available</span>
                    </div>
                  </div>

                  {/* Preview Text */}
                  <div className="p-3 bg-muted/50 rounded border-l-2 border-primary/30">
                    <p className="text-xs text-muted-foreground mb-1">Preview:</p>
                    <p className="text-sm font-mono leading-relaxed">
                      {job.correctText.substring(0, 60)}...
                    </p>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm" className="flex-1">
                      <Edit className="w-3 h-3 mr-1" />
                      Edit
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="text-destructive border-destructive hover:bg-destructive hover:text-destructive-foreground"
                      onClick={() => handleDeleteJob(job.id)}
                    >
                      <Trash2 className="w-3 h-3" />
                    </Button>
                  </div>
                  
                  <div className="text-xs text-muted-foreground text-center pt-2">
                    Created {new Date(job.createdAt).toLocaleDateString()}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Empty State */}
        {jobs.length === 0 && (
          <Card>
            <CardContent className="text-center py-12">
              <div className="text-6xl mb-4">📝</div>
              <h3 className="text-xl font-semibold mb-2">No jobs created yet</h3>
              <p className="text-muted-foreground mb-4">
                Create your first typing job to get started
              </p>
              <Button onClick={() => setIsCreateDialogOpen(true)}>
                <Plus className="w-4 h-4 mr-2" />
                Create First Job
              </Button>
            </CardContent>
          </Card>
        )}
      </main>
    </div>
  );
};

export default ManageJobs;