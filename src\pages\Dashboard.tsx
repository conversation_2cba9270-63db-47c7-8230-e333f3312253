import { useState } from 'react';
import Navigation from '@/components/Navigation';
import StatsCard from '@/components/StatsCard';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Target, 
  Zap, 
  Trophy, 
  Clock, 
  ArrowRight,
  Flame,
  TrendingUp
} from 'lucide-react';
import { currentUser, mockJobs, userJobs, badges } from '@/data/mockData';
import { Link } from 'react-router-dom';

const Dashboard = () => {
  const [greeting, setGreeting] = useState(() => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good morning';
    if (hour < 17) return 'Good afternoon';
    return 'Good evening';
  });

  const recentJobs = userJobs.slice(0, 3);
  const availableJobs = mockJobs.slice(0, 2);
  const earnedBadges = badges.filter(b => b.earned);

  return (
    <div className="min-h-screen bg-background">
      <Navigation userRole="user" />
      
      <main className="ml-64 p-8">
        {/* Welcome Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-foreground mb-2">
            {greeting}, {currentUser.name}! 👋
          </h1>
          <p className="text-muted-foreground">
            Ready to earn some points? You're on a {currentUser.streak}-day streak!
          </p>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <StatsCard
            title="Total Points"
            value={currentUser.points.toLocaleString()}
            change={{ value: 12, type: 'increase' }}
            icon={<Target className="w-4 h-4 text-primary" />}
          />
          <StatsCard
            title="Accuracy"
            value={`${currentUser.accuracy}%`}
            change={{ value: 2.1, type: 'increase' }}
            icon={<Zap className="w-4 h-4 text-accent" />}
          />
          <StatsCard
            title="Current Level"
            value={currentUser.level}
            badge={{ text: "Elite", variant: "outline" }}
            icon={<Trophy className="w-4 h-4 text-warning" />}
          />
          <StatsCard
            title="Streak"
            value={`${currentUser.streak} days`}
            icon={<Flame className="w-4 h-4 text-destructive" />}
          />
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Available Jobs */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle className="text-xl">Available Jobs</CardTitle>
                <Link to="/jobs">
                  <Button variant="outline" size="sm">
                    View All
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                </Link>
              </CardHeader>
              <CardContent className="space-y-4">
                {availableJobs.map((job) => (
                  <div key={job.id} className="p-4 border rounded-lg hover:shadow-md transition-shadow">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-semibold text-foreground">{job.title}</h3>
                      <Badge variant="secondary">{job.points} pts</Badge>
                    </div>
                    <div className="flex items-center gap-4 text-sm text-muted-foreground mb-3">
                      <div className="flex items-center gap-1">
                        <Clock className="w-3 h-3" />
                        {job.timeLimit} min
                      </div>
                      <Badge 
                        variant="outline" 
                        className={
                          job.difficulty === 'easy' ? 'text-accent' : 
                          job.difficulty === 'medium' ? 'text-warning' : 
                          'text-destructive'
                        }
                      >
                        {job.difficulty}
                      </Badge>
                    </div>
                    <Link to={`/jobs/${job.id}`}>
                      <Button size="sm" className="w-full">
                        Start Job
                      </Button>
                    </Link>
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Recent Activity */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Recent Activity</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {recentJobs.map((job) => (
                  <div key={job.id} className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                    <div>
                      <p className="font-medium text-sm">Job Completed</p>
                      <p className="text-xs text-muted-foreground">
                        {job.accuracy}% accuracy
                      </p>
                    </div>
                    <div className="text-right">
                      <Badge variant="secondary">+{job.pointsEarned}</Badge>
                      <p className="text-xs text-muted-foreground mt-1">
                        {new Date(job.submittedAt).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                ))}
                <Link to="/progress">
                  <Button variant="ghost" size="sm" className="w-full">
                    View Progress
                    <TrendingUp className="w-4 h-4 ml-2" />
                  </Button>
                </Link>
              </CardContent>
            </Card>

            {/* Achievements */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Recent Achievements</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {earnedBadges.slice(0, 3).map((badge) => (
                  <div key={badge.id} className="flex items-center gap-3 p-3 bg-accent/10 rounded-lg border border-accent/20">
                    <div className="text-2xl">{badge.icon}</div>
                    <div>
                      <p className="font-medium text-sm">{badge.name}</p>
                      <p className="text-xs text-muted-foreground">{badge.description}</p>
                    </div>
                  </div>
                ))}
                <Link to="/achievements">
                  <Button variant="ghost" size="sm" className="w-full">
                    View All Achievements
                  </Button>
                </Link>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  );
};

export default Dashboard;